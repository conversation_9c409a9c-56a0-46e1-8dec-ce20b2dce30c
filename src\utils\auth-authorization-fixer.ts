/**
 * Comprehensive Authentication and Authorization Fixer
 * Handles 401 Unauthorized errors, authentication state, and RLS policy issues
 */

import { supabase } from '@/integrations/supabase/client';
import { showErrorToast, showWarningToast, showSuccessToast } from './comprehensive-toast-system';
import { safeWindow } from './safe-type-utils';

export interface AuthState {
  isAuthenticated: boolean;
  user: any | null;
  session: any | null;
  lastChecked: Date;
  errorCount: number;
  lastError?: string;
}

export interface AuthFixResult {
  success: boolean;
  message: string;
  fixes: string[];
  authState: AuthState;
}

export class AuthAuthorizationFixer {
  private static instance: AuthAuthorizationFixer;
  private authState: AuthState;
  private protectedQueries: Set<string> = new Set();
  private isInitialized = false;

  static getInstance(): AuthAuthorizationFixer {
    if (!AuthAuthorizationFixer.instance) {
      AuthAuthorizationFixer.instance = new AuthAuthorizationFixer();
    }
    return AuthAuthorizationFixer.instance;
  }

  constructor() {
    this.authState = {
      isAuthenticated: false,
      user: null,
      session: null,
      lastChecked: new Date(),
      errorCount: 0
    };

    // Define queries that require authentication
    this.protectedQueries = new Set([
      'activity_logs',
      'user_presence',
      'profiles',
      'reports',
      'tasks',
      'departments',
      'projects'
    ]);
  }

  /**
   * Initialize comprehensive auth and authorization fixing
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🔧 Initializing auth and authorization fixer...');

    // Check initial auth state
    await this.checkAuthState();
    
    // Setup auth monitoring
    this.setupAuthMonitoring();
    
    // Setup 401 error handling
    this.setup401ErrorHandling();
    
    // Setup protected query filtering
    this.setupProtectedQueryFiltering();
    
    // Setup session management
    this.setupSessionManagement();

    this.isInitialized = true;
    console.log('✅ Auth and authorization fixer initialized');
  }

  /**
   * Check current authentication state
   */
  async checkAuthState(): Promise<AuthState> {
    console.log('🔍 Checking authentication state...');

    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.warn('⚠️ Auth session error:', error.message);
        this.authState = {
          isAuthenticated: false,
          user: null,
          session: null,
          lastChecked: new Date(),
          errorCount: this.authState.errorCount + 1,
          lastError: error.message
        };
      } else if (session?.user) {
        console.log('✅ User is authenticated:', session.user.email);
        this.authState = {
          isAuthenticated: true,
          user: session.user,
          session: session,
          lastChecked: new Date(),
          errorCount: 0
        };
      } else {
        console.log('ℹ️ User is not authenticated');
        this.authState = {
          isAuthenticated: false,
          user: null,
          session: null,
          lastChecked: new Date(),
          errorCount: 0
        };
      }

      return this.authState;
    } catch (error: any) {
      console.error('❌ Auth state check failed:', error);
      this.authState = {
        isAuthenticated: false,
        user: null,
        session: null,
        lastChecked: new Date(),
        errorCount: this.authState.errorCount + 1,
        lastError: error.message
      };
      return this.authState;
    }
  }

  /**
   * Setup authentication monitoring
   */
  private setupAuthMonitoring(): void {
    // Monitor auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth state change:', event);

      switch (event) {
        case 'SIGNED_IN':
          this.authState = {
            isAuthenticated: true,
            user: session?.user || null,
            session: session,
            lastChecked: new Date(),
            errorCount: 0
          };
          console.log('✅ User signed in successfully');
          break;

        case 'SIGNED_OUT':
          this.authState = {
            isAuthenticated: false,
            user: null,
            session: null,
            lastChecked: new Date(),
            errorCount: 0
          };
          console.log('ℹ️ User signed out');
          this.clearAuthData();
          break;

        case 'TOKEN_REFRESHED':
          if (session) {
            this.authState.session = session;
            this.authState.user = session.user;
            this.authState.lastChecked = new Date();
            console.log('✅ Token refreshed successfully');
          }
          break;

        default:
          console.log('🔄 Auth event:', event);
      }
    });
  }

  /**
   * Setup 401 error handling
   */
  private setup401ErrorHandling(): void {
    // Override fetch to handle 401 errors
    const originalFetch = window.fetch;
    
    window.fetch = async (input, init) => {
      const url = typeof input === 'string' ? input : input.url;
      
      try {
        const response = await originalFetch.call(window, input, init);
        
        // Handle 401 Unauthorized errors
        if (response.status === 401 && url.includes('supabase.co')) {
          console.warn('🔧 401 Unauthorized detected for:', url);
          await this.handle401Error(url);
        }
        
        return response;
      } catch (error) {
        throw error;
      }
    };
  }

  /**
   * Handle 401 Unauthorized errors
   */
  private async handle401Error(url: string): Promise<void> {
    console.log('🔧 Handling 401 error for URL:', url);

    // Check if this is a protected query
    const isProtectedQuery = Array.from(this.protectedQueries).some(table => url.includes(table));
    
    if (isProtectedQuery) {
      console.log('🔒 Protected query detected, checking auth state...');
      
      // Check current auth state
      await this.checkAuthState();
      
      if (!this.authState.isAuthenticated) {
        console.log('❌ User not authenticated, preventing protected queries');
        
        // Don't show error toast for every 401 - just log it
        console.warn('🔒 Blocked unauthorized access to:', url);
        
        // Only show toast if this is the first few errors
        if (this.authState.errorCount <= 2) {
          showWarningToast({
            title: 'Authentication Required',
            description: 'Please sign in to access this feature.',
            duration: 4000
          });
        }
        
        return;
      } else {
        console.log('✅ User is authenticated, 401 might be RLS policy issue');
        
        // This might be an RLS policy issue
        if (this.authState.errorCount <= 1) {
          showWarningToast({
            title: 'Access Restricted',
            description: 'You may not have permission to access this resource.',
            duration: 4000
          });
        }
      }
    }
  }

  /**
   * Setup protected query filtering
   */
  private setupProtectedQueryFiltering(): void {
    // Monitor Supabase queries and filter unauthorized ones
    const originalFrom = supabase.from.bind(supabase);
    
    supabase.from = (table: string) => {
      const query = originalFrom(table);
      
      // Check if this is a protected table
      if (this.protectedQueries.has(table)) {
        console.log(`🔒 Protected table access: ${table}`);
        
        // Check auth state before allowing query
        if (!this.authState.isAuthenticated) {
          console.warn(`❌ Blocked unauthorized access to table: ${table}`);
          
          // Return a mock query that will fail gracefully
          return {
            ...query,
            select: () => Promise.resolve({ data: null, error: { message: 'Authentication required' } }),
            insert: () => Promise.resolve({ data: null, error: { message: 'Authentication required' } }),
            update: () => Promise.resolve({ data: null, error: { message: 'Authentication required' } }),
            delete: () => Promise.resolve({ data: null, error: { message: 'Authentication required' } })
          } as any;
        }
      }
      
      return query;
    };
  }

  /**
   * Setup session management
   */
  private setupSessionManagement(): void {
    // Check session validity every 5 minutes
    setInterval(async () => {
      if (this.authState.isAuthenticated) {
        console.log('🔍 Checking session validity...');
        await this.checkAuthState();
        
        if (!this.authState.isAuthenticated) {
          console.warn('⚠️ Session expired, clearing auth data');
          this.clearAuthData();
          
          showWarningToast({
            title: 'Session Expired',
            description: 'Your session has expired. Please sign in again.',
            duration: 5000
          });
        }
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  /**
   * Clear authentication data
   */
  private clearAuthData(): void {
    try {
      // Clear localStorage
      localStorage.removeItem('supabase.auth.token');
      localStorage.removeItem('sb-dvflgnqwbsjityrowatf-auth-token');
      
      // Clear sessionStorage
      sessionStorage.removeItem('supabase.auth.token');
      sessionStorage.removeItem('sb-dvflgnqwbsjityrowatf-auth-token');
      
      console.log('✅ Auth data cleared');
    } catch (error: any) {
      console.warn('⚠️ Error clearing auth data:', error.message);
    }
  }

  /**
   * Force authentication check
   */
  async forceAuthCheck(): Promise<AuthFixResult> {
    console.log('🔧 Forcing authentication check...');
    
    const fixes: string[] = [];
    
    // Clear any stale auth data
    this.clearAuthData();
    fixes.push('Cleared stale authentication data');
    
    // Check current auth state
    await this.checkAuthState();
    fixes.push('Performed authentication state check');
    
    // Reset error count
    this.authState.errorCount = 0;
    fixes.push('Reset authentication error count');

    const result: AuthFixResult = {
      success: this.authState.isAuthenticated,
      message: this.authState.isAuthenticated 
        ? 'Authentication state verified successfully'
        : 'User is not authenticated - please sign in',
      fixes,
      authState: this.authState
    };

    if (result.success) {
      showSuccessToast({
        title: 'Authentication Verified',
        description: 'Your authentication state has been verified.',
        duration: 3000
      });
    } else {
      showWarningToast({
        title: 'Authentication Required',
        description: 'Please sign in to access protected features.',
        duration: 4000
      });
    }

    return result;
  }

  /**
   * Get authentication state
   */
  getAuthState(): AuthState {
    return { ...this.authState };
  }

  /**
   * Check if user has permission for specific action
   */
  hasPermission(action: string, resource?: string): boolean {
    if (!this.authState.isAuthenticated) {
      return false;
    }

    // Basic permission check - can be extended based on user roles
    const user = this.authState.user;
    if (!user) return false;

    // Admin users have all permissions
    if (user.user_metadata?.role === 'admin') {
      return true;
    }

    // Add more specific permission logic here
    return true; // Default to allow for authenticated users
  }

  /**
   * Get authentication diagnostics
   */
  getDiagnostics(): any {
    return {
      authState: this.authState,
      protectedQueries: Array.from(this.protectedQueries),
      isInitialized: this.isInitialized,
      supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
      hasApiKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const authAuthorizationFixer = AuthAuthorizationFixer.getInstance();

// Export utility functions
export const initializeAuthAuthorizationFixer = () => authAuthorizationFixer.initialize();
export const checkAuthState = () => authAuthorizationFixer.checkAuthState();
export const forceAuthCheck = () => authAuthorizationFixer.forceAuthCheck();
export const getAuthState = () => authAuthorizationFixer.getAuthState();
export const hasPermission = (action: string, resource?: string) => authAuthorizationFixer.hasPermission(action, resource);
export const getAuthDiagnostics = () => authAuthorizationFixer.getDiagnostics();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  safeWindow.set('authAuthorizationFixer', authAuthorizationFixer);
  safeWindow.set('checkAuthState', checkAuthState);
  safeWindow.set('forceAuthCheck', forceAuthCheck);
  safeWindow.set('getAuthDiagnostics', getAuthDiagnostics);
  
  console.log('🔧 Auth Authorization Fixer loaded. Available commands:');
  console.log('  - checkAuthState()');
  console.log('  - forceAuthCheck()');
  console.log('  - getAuthDiagnostics()');
}

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  setTimeout(() => {
    authAuthorizationFixer.initialize();
  }, 800);
}

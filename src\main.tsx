import { ThemeProvider } from '@/components/theme-provider'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
// Removed StrictMode import to prevent insertBefore DOM manipulation errors
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Check environment variables for production deployment
import '@/utils/env-check'

// Fix runtime errors early in the application startup
import '@/utils/fix-runtime-errors'

// Register service worker for PWA functionality

// Import enhanced cache and WebSocket managers
import { enhancedCacheManager } from '@/utils/enhanced-cache-manager';
import { webSocketManager } from '@/utils/websocket-manager';
import { startupInitializer } from '@/utils/startup-initializer';
import { enhancedRealtimeService } from '@/services/enhanced-realtime-service';
import { comprehensiveSystemFixer } from '@/utils/comprehensive-system-fixer';
import { comprehensiveToastSystem } from '@/utils/comprehensive-toast-system';
import { comprehensiveFormHandler } from '@/utils/comprehensive-form-handler';
import { authErrorFix } from '@/utils/auth-error-fix';
import { safeWindow } from '@/utils/safe-type-utils';
import { functionErrorAnalyzer } from '@/utils/function-error-analyzer';
import { typeScriptErrorFixer } from '@/utils/typescript-error-fixer';
import { supabaseConnectivityFixer } from '@/utils/supabase-connectivity-fixer';
import { comprehensiveSchemaFixer } from '@/utils/comprehensive-schema-fixer';

// Conditionally import cache utilities only in development
if (import.meta.env.DEV) {
  import('@/utils/cacheManager');
}

// Removed AOS initialization to prevent DOM manipulation conflicts with React
// AOS was causing "Failed to execute 'insertBefore' on 'Node'" errors

// Create a client with optimized performance settings and aggressive cache clearing
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1 * 60 * 1000, // 1 minute for faster cache invalidation
      gcTime: 2 * 60 * 1000, // 2 minutes cache retention for faster clearing (renamed from cacheTime in v5)
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false, // Disable refetch on window focus for better performance
      refetchOnReconnect: true, // Refetch when connection is restored
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
})

// Initialize cache management only in development
if (import.meta.env.DEV) {
  // Dynamic import to avoid circular dependencies in production
  import('@/utils/cacheManager').then(({ cacheManager }) => {
    cacheManager.setQueryClient(queryClient);

    // Clear cache on page load/refresh to handle server restarts
    const clearCacheOnLoad = () => {
      if (cacheManager.shouldClearCache(30)) {
        console.log('🧹 Clearing React Query cache due to server restart or time threshold');
        cacheManager.clearAll();
      }
      cacheManager.handleServerRestart();
    };

    clearCacheOnLoad();
    cacheManager.startPeriodicCleanup(60);
  }).catch(() => {
    // Silently fail if cache manager is not available
  });
}



// Make cache clearing available globally for console access (development only)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  safeWindow.set('clearCache', () => {
    console.log('🧹 Clearing cache from console...');
    import('@/utils/cacheManager').then(({ cacheManager }) => {
      cacheManager.clearAll();
      localStorage.removeItem('lastCacheClear');
      localStorage.removeItem('serverStartTime');
      console.log('✅ Cache cleared! Reload the page for best results.');
    });
    return 'Cache cleared successfully!';
  });

  safeWindow.set('clearDashboardCache', () => {
    console.log('🧹 Clearing dashboard cache from console...');
    import('@/utils/cacheManager').then(({ cacheManager }) => {
      cacheManager.clearDashboardCache();
      console.log('✅ Dashboard cache cleared!');
    });
    return 'Dashboard cache cleared successfully!';
  });

  // Add database debugging helper
  safeWindow.set('fixDatabaseErrors', () => {
    console.log('🔧 Database Error Fix Helper');
    console.log('If you are seeing project assignment errors, try these commands:');
    console.log('1. checkDatabaseSchema() - Check current table structure');
    console.log('2. testProjectAssignment() - Test assignment creation');
    console.log('3. getDatabaseInfo() - Get detailed database information');
    console.log('4. analyzeDatabase() - Comprehensive database analysis');
    console.log('5. checkAssetsTable() - Check assets inventory table');
    console.log('6. testDashboardData() - Test dashboard data sources');
    console.log('7. quickFixDashboard() - Create sample data for empty dashboard');
    console.log('8. fixDuplicateDepartments() - Fix duplicate department errors');
    console.log('9. quickFixDuplicates() - Quick fix for current duplicate error');
    console.log('10. createSampleBatteryReports() - Create sample battery reports for testing');
    console.log('11. migrateToCustomFolders() - Migrate to new custom folder system');
    console.log('');
    console.log('These functions are available after the app loads.');
    return 'Database debugging commands listed above.';
  });


}

// Initialize enhanced cache manager with QueryClient
enhancedCacheManager.initialize(queryClient);

// Initialize startup system (this will handle WebSocket and other initializations)
startupInitializer.initialize().catch(error => {
  console.warn('Startup initialization failed:', error);
});

const rootElement = document.getElementById('root');
if (rootElement) {
  ReactDOM.createRoot(rootElement).render(
  // Removed StrictMode to prevent insertBefore DOM manipulation errors
  <ThemeProvider defaultTheme="dark" storageKey="ctnl-theme">
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </ThemeProvider>
  );
} else {
  console.error('Root element not found');
}

// Initialize PWA features after app renders
if (import.meta.env.PROD) {
  // Register service worker in production
  import('@/utils/sw-registration').then(({ registerServiceWorker }) => {
    registerServiceWorker();
    console.log('🚀 PWA features initialized');
  }).catch((error) => {
    console.warn('PWA initialization failed:', error);
  });
}

// DISABLED: Initialize comprehensive app fixes - causing React DOM conflicts
// import('@/utils/comprehensive-app-fixer').then(({ runAllFixes }) => {
//   // Run fixes after a short delay to allow app to initialize
//   setTimeout(async () => {
//     try {
//       const result = await runAllFixes();
//       if (result.success) {
//         console.log('✅ All app fixes applied successfully');
//       } else {
//         console.warn('⚠️ Some app fixes failed:', result.results);
//       }
//     } catch (error) {
//       console.warn('❌ App fixes failed:', error);
//     }
//   }, 2000);
// }).catch((error) => {
//   console.warn('Failed to load app fixer:', error);
// });
// Note: App fixes disabled to prevent insertBefore DOM manipulation errors

// DISABLED: Apply comprehensive system fixes - causing React DOM conflicts
// setTimeout(() => {
//   console.log('🔧 Applying comprehensive system fixes...');
//   comprehensiveSystemFixer.fixAllSystemIssues().then((result) => {
//     if (result.success) {
//       console.log('✅ All system fixes applied successfully');
//       console.log('📋 Fix results:', result.results);
//     } else {
//       console.warn('⚠️ Some system fixes failed:', result.results);
//     }
//   }).catch((error) => {
//     console.warn('❌ System fixes failed:', error);
//   });
// }, 3000);
// Note: System fixes disabled to prevent insertBefore DOM manipulation errors

// DISABLED: Initialize comprehensive toast system - causing React DOM conflicts
// setTimeout(() => {
//   console.log('🍞 Initializing comprehensive toast system...');
//   comprehensiveToastSystem.initialize();
// }, 1000);

// DISABLED: Initialize comprehensive form handler - causing React DOM conflicts
// setTimeout(() => {
//   console.log('📝 Initializing comprehensive form handler...');
//   comprehensiveFormHandler.initialize();
// }, 1500);
// Note: Toast and form systems disabled to prevent DOM manipulation conflicts

// Initialize comprehensive schema fixer (highest priority)
setTimeout(() => {
  console.log('🔧 Initializing comprehensive schema fixer...');
  comprehensiveSchemaFixer.initialize();
}, 100);

// Initialize Supabase connectivity fixer
setTimeout(() => {
  console.log('🔧 Initializing Supabase connectivity fixer...');
  supabaseConnectivityFixer.initialize();
}, 200);

// Initialize auth error fix
setTimeout(() => {
  console.log('🔧 Initializing auth error fix...');
  authErrorFix.initialize();
}, 500);

// Initialize function error analyzer
setTimeout(() => {
  console.log('🔍 Initializing function error analyzer...');
  functionErrorAnalyzer.runCompleteAnalysis().then((result) => {
    console.log('📊 Function analysis complete:', result.summary);
    if (result.summary.criticalErrors > 0) {
      console.warn('⚠️ Critical function errors detected:', result.summary.criticalErrors);
    }
  });
}, 2000);

// Initialize TypeScript error fixer
setTimeout(() => {
  console.log('🔧 Initializing TypeScript error fixer...');
  typeScriptErrorFixer.initialize();
}, 2500);
